import { Point, point } from '@flatten-js/core';
import { <PERSON>rrorHandlerDecorator, UIPointerEventData } from '@viclass/editor.core';
import { syncPreviewCommands, syncRemovePreviewCmd } from '../cmd';
import { geoDefaultHandlerFn, GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { GeoElConstructionRequest, IntersectionPointToolState, RenderVertex, StrokeType } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue, pVertex } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import {
    ElementSelector,
    repeat,
    RepeatSelector,
    SelectableType,
    SelectorOptions,
    then,
    ThenSelector,
    vertex,
} from '../selectors';
import { nLines, SelectedStroke, strk } from '../selectors/common.selection';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { buildIntersectionRequest, getElementConstructionDetails } from './util.construction';
import { calculateIntersections, filterVisibleIntersections, getOrderedIntersections } from './util.intersections';
import {
    assignNames,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isElementLine,
    pointsEqual,
    remoteConstruct,
} from './util.tool';

/**
 * Intersection Point Tool - Creates intersection points between two geometric elements
 * Follows standardized geometry tool patterns for selection, preview, and construction
 */
export class IntersectionPointTool extends GeometryTool<IntersectionPointToolState> {
    override readonly toolType: GeometryToolType = 'IntersectionPointTool';

    declare selLogic: ThenSelector;
    private pQ = new PreviewQueue();

    // Public for message component
    intersections: RenderVertex[] = []; // Only intersections within bounds

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    /**
     * Creates selection logic using standardized selector pattern
     */
    private createSelLogic() {
        this.selLogic = then(
            [
                nLines(this.pQ, this.pointerHandler.cursor, {
                    count: 2,
                    onComplete: this.performIntersectionPreview.bind(this),
                    onPartialSelection: (
                        _newSel: SelectedStroke,
                        _curSel: SelectedStroke[],
                        selector: ElementSelector<SelectedStroke[], SelectorOptions<SelectedStroke[]>>,
                        _doc: GeoDocCtrl
                    ) => {
                        this.updateToolState(selector.selected?.length ?? 0);
                        return true;
                    },
                }),
                repeat(
                    vertex({
                        preview: true,
                        renderEl: true,
                        genPreview: false,
                        syncPreview: false,
                        previewQueue: this.pQ,
                        cfunc: (el: RenderVertex, _doc: GeoDocCtrl) =>
                            this.intersections.some(pt => pt.relIndex === el.relIndex),
                    }),
                    {
                        onPartialSelection: (
                            newSel: SelectableType,
                            _curSel: SelectableType[],
                            selector: ElementSelector<SelectableType[], SelectorOptions<SelectableType[]>>,
                            doc: GeoDocCtrl
                        ) => {
                            const [element1, element2] = this.selLogic.selected;
                            const el1 = strk(element1 as StrokeType);
                            const el2 = strk(element2 as StrokeType);
                            this.performConstruction(doc, el1, el2, newSel as RenderVertex);
                            this.updateToolState(2 + (selector.selected?.length ?? 0));
                            return this.intersections.length !== 0;
                        },
                    }
                ),
            ],
            {
                flatten: true,
            }
        );
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        this.selLogic.trySelect(event, ctrl);
        this.pQ.flush(ctrl);
    }

    private updateToolState(count: number) {
        this.toolState.selectedCount = count;
        this.toolbar.update(this.toolType, this.toolState);
    }

    override resetState() {
        this.intersections = [];
        super.resetState();
        this.selLogic?.reset();
        this.createSelLogic();
        this.updateToolState(0);
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType === 'pointerdown') if (!this.shouldHandleClick(event)) return event;

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType === 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();
        return event;
    }

    private extractIntersections(
        docCtrl: GeoDocCtrl,
        el1: StrokeType,
        el2: StrokeType
    ): {
        allIntersections: Point[];
        allIntersectionsOrdered: Point[];
        visibleIntersections: Point[];
    } {
        const allIntersections = calculateIntersections(el1, el2, docCtrl) || [];
        const visibleIntersections = filterVisibleIntersections(allIntersections, el1, el2, docCtrl);
        const allIntersectionsOrdered = getOrderedIntersections(el1, el2, allIntersections, docCtrl);

        return {
            allIntersections,
            allIntersectionsOrdered,
            visibleIntersections,
        };
    }

    /**
     * Generates intersection preview when two elements are selected
     * Calculates both all intersections and visible intersections
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performIntersectionPreview(selector: RepeatSelector<SelectedStroke>, docCtrl: GeoDocCtrl) {
        const [element1, element2] = selector.selected;

        const el1 = strk(element1);
        const el2 = strk(element2);

        const { visibleIntersections } = this.extractIntersections(docCtrl, el1, el2);
        if (visibleIntersections.length === 0) {
            this.resetState();
            return;
        }

        // Special case for line-line: construct immediately
        if (isElementLine(el1) && isElementLine(el2)) {
            await this.performConstruction(
                docCtrl,
                el1,
                el2,
                pVertex(-33, [visibleIntersections[0].x, visibleIntersections[0].y, 0])
            );
            this.resetState();
            return;
        }

        this.intersections = visibleIntersections.map((pt, idx) => pVertex(-33 - idx, [pt.x, pt.y, 0]));
        this.intersections.forEach(pt => syncPreviewCommands(pt, docCtrl));
    }

    /**
     * Performs construction of selected intersection point
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstruction(
        docCtrl: GeoDocCtrl,
        el1: StrokeType,
        el2: StrokeType,
        intersectionSelected: RenderVertex
    ) {
        const { pcs, points } = await assignNames(
            docCtrl,
            [intersectionSelected],
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            'Giao Điểm'
        );

        if (!pcs || !points) {
            this.resetState();
            return;
        }

        const construction = this.buildConstructionRequest(docCtrl, el1, el2, intersectionSelected, points[0].name);
        if (!construction) return;

        try {
            await remoteConstruct(docCtrl, construction, [], this.editor.geoGateway, 'giao điểm');

            // Update remaining preview points
            this.intersections = this.intersections.filter(pt => pt.relIndex != intersectionSelected.relIndex);
            if (this.intersections.length === 0) this.resetState();
            else syncRemovePreviewCmd(intersectionSelected, docCtrl);
        } catch (e) {
            this.resetState();
            throw e;
        }
    }

    /**
     * Builds construction request based on selected elements and intersection point
     */
    private buildConstructionRequest(
        docCtrl: GeoDocCtrl,
        el1: StrokeType,
        el2: StrokeType,
        selectedIntersection: RenderVertex,
        pointName: string
    ): GeoElConstructionRequest | undefined {
        const paramA = getElementConstructionDetails(el1);
        const paramB = getElementConstructionDetails(el2);

        // Calculate all intersections first
        const intersections = calculateIntersections(el1, el2, docCtrl) || [];
        if (intersections.length === 0) {
            this.resetState();
            return undefined;
        }

        // Filter visible intersections based on element bounds
        const visibleIntersections = filterVisibleIntersections(intersections, el1, el2, docCtrl).map((pt, idx) =>
            pVertex(-33 - idx, [pt.x, pt.y, 0])
        );
        if (visibleIntersections.length === 0) {
            this.resetState();
            return undefined;
        }

        // Determine intersection type
        const cgName = this.getIntersectionTypeName(el1, el2);
        if (!cgName) return undefined;

        return buildIntersectionRequest({
            cgName,
            outputName: pointName,
            paramA,
            paramB,
            nth:
                cgName === 'LineLine'
                    ? undefined
                    : intersections.length === 1
                      ? 0
                      : this.calculateNthParameter(docCtrl, el1, el2, selectedIntersection),
        });
    }

    /**
     * Gets intersection type name for construction
     */
    private getIntersectionTypeName(element1: StrokeType, element2: StrokeType): string | undefined {
        if (isElementLine(element1) && isElementLine(element2)) return 'LineLine';
        if (
            (isElementLine(element1) && element2.type === 'RenderCircle') ||
            (element1.type === 'RenderCircle' && isElementLine(element2))
        )
            return 'LineCircle';
        if (
            (isElementLine(element1) && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && isElementLine(element2))
        )
            return 'LineSector';
        if (
            (isElementLine(element1) && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && isElementLine(element2))
        )
            return 'LineEllipse';
        if (element1.type === 'RenderCircle' && element2.type === 'RenderCircle') return 'CircleCircle';
        if (
            (element1.type === 'RenderCircle' && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && element2.type === 'RenderCircle')
        )
            return 'CircleSector';
        if (
            (element1.type === 'RenderCircle' && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && element2.type === 'RenderCircle')
        )
            return 'CircleEllipse';
        if (element1.type === 'RenderSector' && element2.type === 'RenderSector') return 'SectorSector';
        if (
            (element1.type === 'RenderSector' && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && element2.type === 'RenderSector')
        )
            return 'SectorEllipse';
        if (element1.type === 'RenderEllipse' && element2.type === 'RenderEllipse') return 'EllipseEllipse';
        return undefined;
    }

    /**
     * Calculates nth parameter for intersection point selection
     * Uses the same ordering logic as the backend to ensure consistent nth parameter calculation
     */
    private calculateNthParameter(
        docCtrl: GeoDocCtrl,
        el1: StrokeType,
        el2: StrokeType,
        selectedIntersection: RenderVertex
    ): number {
        const { allIntersectionsOrdered, visibleIntersections } = this.extractIntersections(docCtrl, el1, el2);

        if (visibleIntersections.length === 0) return undefined;
        if (visibleIntersections.length === 1) return 0;

        // Find the position in the ordered intersections array
        const selectedIntersectionPt = point(selectedIntersection.coords[0], selectedIntersection.coords[1]);
        const orderedIndex = allIntersectionsOrdered.findIndex(pt => pointsEqual(pt, selectedIntersectionPt));

        // Return 0-based index to match backend expectation (backend uses nth directly)
        return orderedIndex >= 0 ? orderedIndex : undefined;
    }
}
