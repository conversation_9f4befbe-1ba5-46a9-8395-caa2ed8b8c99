import { Point, point, vector } from '@flatten-js/core';
import { syncRemovePreviewCmd } from '../cmd';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    GeoElConstructionRequest,
    ParallelPerpendicularPointToolState,
    RenderCircle,
    RenderEllipse,
    RenderLine,
    RenderSector,
    RenderVertex,
    StrokeType,
} from '../model';
import { GeoEpsilon, GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue, pVertex } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import {
    ElementSelector,
    or,
    SelectedStroke,
    SelectorOptions,
    strk,
    stroke,
    then,
    ThenSelector,
    vert,
    vertex,
    VertexOnStroke,
    vertexOnStroke,
} from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    calculateIntersections,
    calculateLineLineIntersection,
    filterVisibleIntersections,
    getOrderedIntersections,
} from './util.intersections';
import {
    assignNames,
    buildPreviewLineRenderProp,
    buildPreviewVertexRenderProp,
    distance2Point,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isElementCurve,
    isElementLine,
    projectPointOntoLine,
    remoteConstruct,
} from './util.tool';

/**
 * Base class for line-based geometry tools (parallel, perpendicular, etc.)
 * Contains shared functionality for tools that create lines based on existing lines and points
 */
export abstract class BaseParallelPerpendicularTool extends GeometryTool<ParallelPerpendicularPointToolState> {
    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();

    protected get baseLine(): RenderLine {
        return this.selLogic.selected?.[0] as RenderLine;
    }

    protected get throughPoint(): RenderVertex {
        return this.selLogic.selected?.[1] as RenderVertex;
    }

    protected readonly previewLineRelIdx = -20;
    protected readonly previewBaseLineRelIdx = -99;
    protected readonly previewThroughPointRelIdx = -999;

    protected previewLine(docCtrl: GeoDocCtrl): RenderLine {
        return docCtrl.rendererCtrl.previewElAt(this.previewLineRelIdx) as RenderLine;
    }

    private previewBaseLine(docCtrl: GeoDocCtrl): RenderLine {
        return docCtrl.rendererCtrl.previewElAt(this.previewBaseLineRelIdx) as RenderLine;
    }

    private previewThroughPoint(docCtrl: GeoDocCtrl): RenderVertex {
        return docCtrl.rendererCtrl.previewElAt(this.previewThroughPointRelIdx) as RenderVertex;
    }

    protected get finalVertexStroke(): RenderLine | RenderCircle | RenderEllipse | RenderSector {
        const finalVertexSelection = this.selLogic.selected?.[2];
        if (!finalVertexSelection) return undefined;

        const stroke = strk(finalVertexSelection as SelectedStroke | StrokeType);
        if (isElementLine(stroke) || isElementCurve(stroke)) return stroke;
        return undefined;
    }

    protected get finalVertex(): RenderVertex {
        const finalVertexSelection = this.selLogic.selected?.[2];
        if (!finalVertexSelection) return undefined;
        return vert(finalVertexSelection as RenderVertex);
    }

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        this.selLogic?.reset();
        this.createSelLogic();
        this.updateToolState(0);
        super.resetState();
    }

    protected abstract calculateLinePreviewVector(docCtrl: GeoDocCtrl, baseLine: RenderLine): number[];

    protected abstract buildSimpleLineConstruction(
        lineName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex
    ): GeoElConstructionRequest;

    protected abstract buildLineSegmentConstruction(
        combinedName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex,
        scalingFactor: number
    ): GeoElConstructionRequest;

    protected abstract buildLineSegmentWithIntersectionConstruction(
        combinedName: string,
        baseLine: RenderLine,
        intersectLine: RenderLine,
        throughPoint: RenderVertex
    ): GeoElConstructionRequest;

    protected abstract buildCurvedElementConstruction(
        combinedName: string,
        baseLine: RenderLine,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        nthIntersection?: number
    ): GeoElConstructionRequest;

    protected abstract buildSegmentBetweenPointsConstruction(
        combinedName: string,
        baseLine: RenderLine,
        startPoint: RenderVertex,
        endPoint: RenderVertex
    ): GeoElConstructionRequest;

    protected abstract getSimpleConstructionLabel(): string;

    protected abstract getComplexConstructionLabel(): string;

    /**
     * Creates the selection logic using selector pattern with preview
     * Following Pattern: Line -> Point -> Preview -> Final Point Selection
     */
    protected createSelLogic() {
        // First selector: select a line
        const lineSelector = stroke({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
            refinedFilter: el => isElementLine(el),
            onComplete: (_selector: ElementSelector<StrokeType, SelectorOptions<StrokeType>>, _doc: GeoDocCtrl) => {
                this.updateToolStateSelectedCount();
            },
        });

        // Second selector: select a point to define through which the line passes
        const firstPointSelector = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
            onComplete: (_selector: ElementSelector<RenderVertex, SelectorOptions<RenderVertex>>, _doc: GeoDocCtrl) => {
                this.updateToolStateSelectedCount();
            },
        });

        // Third selector: enhanced vertex selector with projection for final point on line
        const finalVertexSelector = or(
            [
                // Option 1: Select free vertex with projection onto line
                vertex({
                    preview: true, // Allow selecting preview elements (including first point if it was a preview)
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    tfunc: (finalVertex: RenderVertex, docCtrl: GeoDocCtrl) =>
                        this.projectOnLine(this.throughPoint, finalVertex, docCtrl),
                    onComplete: (
                        _selector: ElementSelector<RenderVertex, SelectorOptions<RenderVertex>>,
                        _doc: GeoDocCtrl
                    ) => {
                        this.updateToolStateSelectedCount();
                    },
                }),
                // Option 2: Select vertex on stroke with intersection projection
                vertexOnStroke({
                    preview: true,
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    tfunc: (stroke, previewVertex, docCtrl) =>
                        this.projectVertexOnStrokeToIntersection(
                            this.previewLine(docCtrl),
                            stroke,
                            previewVertex,
                            docCtrl
                        ),
                    cfunc: (stroke, docCtrl) =>
                        this.checkStrokeIntersection(this.previewLine(docCtrl), stroke, docCtrl),
                    refinedFilter: el => isElementLine(el) || isElementCurve(el),
                    onComplete: (
                        _selector: ElementSelector<VertexOnStroke, SelectorOptions<VertexOnStroke>>,
                        _doc: GeoDocCtrl
                    ) => {
                        this.updateToolStateSelectedCount();
                    },
                }),
            ],
            { flatten: true }
        );

        // Main selection logic: line -> point -> final vertex
        this.selLogic = then([lineSelector, firstPointSelector, finalVertexSelector], {
            onComplete: async (selector: ThenSelector, docCtrl: GeoDocCtrl) => {
                const finalVertexSelection = selector.selected?.[2];
                const finalVertex = vert(finalVertexSelection as RenderVertex | VertexOnStroke);

                // Check if final vertex is the same as the through point
                if (this.throughPoint.relIndex === finalVertex.relIndex)
                    await this.handleSimpleLineConstruction(
                        docCtrl,
                        this.baseLine,
                        this.throughPoint,
                        this.previewLine(docCtrl)
                    );
                else {
                    const finalVertexStroke = strk(finalVertexSelection as RenderLine);
                    await this.handleComplexLineConstruction(
                        docCtrl,
                        this.baseLine,
                        this.throughPoint,
                        this.previewLine(docCtrl),
                        finalVertex,
                        isElementLine(finalVertexStroke) || isElementCurve(finalVertexStroke)
                            ? finalVertexStroke
                            : undefined
                    );
                }
            },
        });
    }

    private updateToolStateSelectedCount() {
        this.updateToolState(this.selLogic.selected?.length ?? 0);
    }

    private updateToolState(count: number) {
        this.toolState.selectedCount = count;
        this.toolbar.update(this.toolType, this.toolState);
    }

    /**
     * Check function to validate that stroke has intersection with preview line
     * Enhanced to support curved elements
     */
    protected checkStrokeIntersection(
        previewLine: RenderLine,
        finalVertexStroke: StrokeType,
        docCtrl: GeoDocCtrl
    ): boolean {
        if (isElementLine(finalVertexStroke))
            return this.checkLineIntersection(finalVertexStroke as RenderLine, docCtrl);
        if (isElementCurve(finalVertexStroke))
            return this.checkCurvedElementIntersection(
                previewLine,
                finalVertexStroke as RenderCircle | RenderEllipse | RenderSector,
                docCtrl
            );
        return false;
    }

    /**
     * Transform function to project any point onto the line preview
     */
    protected projectOnLine(throughPoint: RenderVertex, finalVertex: RenderVertex, docCtrl: GeoDocCtrl): RenderVertex {
        if (finalVertex.relIndex >= 0) return finalVertex;

        const distance = distance2Point(finalVertex.coords, throughPoint.coords);

        // If user clicks on the same point as through point, keep it there
        if (distance < GeoEpsilon) {
            finalVertex.coords[0] = throughPoint.coords[0];
            finalVertex.coords[1] = throughPoint.coords[1];
            if (finalVertex.coords.length > 2) finalVertex.coords[2] = 0;
            return finalVertex;
        }

        const projectedCoords = projectPointOntoLine(
            finalVertex.coords,
            throughPoint.coords,
            this.previewLine(docCtrl).orderedVector(docCtrl.rendererCtrl)
        );

        if (projectedCoords) {
            finalVertex.coords[0] = projectedCoords[0];
            finalVertex.coords[1] = projectedCoords[1];
            if (finalVertex.coords.length > 2) finalVertex.coords[2] = 0; // Z coordinate
        }

        return finalVertex;
    }

    /**
     * Transform function to project vertex on stroke to intersection with line
     * Enhanced to support curved elements
     */
    protected projectVertexOnStrokeToIntersection(
        previewLine: RenderLine,
        stroke: StrokeType,
        finalVertex: RenderVertex,
        docCtrl: GeoDocCtrl
    ): RenderVertex {
        // If the final vertex is constructed point or on the preview line then keep the coordinates
        if (finalVertex.relIndex >= 0 || previewLine.relIndex === stroke.relIndex) return finalVertex;
        return this.projectFinalVertex(docCtrl, previewLine, finalVertex, stroke);
    }

    /**
     * Check if line intersects with preview line
     */
    protected checkLineIntersection(line: RenderLine, docCtrl: GeoDocCtrl): boolean {
        try {
            const intersections = calculateLineLineIntersection(this.previewLine(docCtrl), line, docCtrl);
            return intersections && intersections.length > 0;
        } catch (error) {
            console.warn('Error checking line intersection:', error);
            return false;
        }
    }

    /**
     * Check if curved element intersects with preview line
     */
    protected checkCurvedElementIntersection(
        previewLine: RenderLine,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        docCtrl: GeoDocCtrl
    ): boolean {
        if (!this.previewLine) return false;

        const visibleIntersections = filterVisibleIntersections(
            calculateIntersections(previewLine, curvedElement, docCtrl),
            previewLine,
            curvedElement,
            docCtrl
        );

        return !!visibleIntersections?.length;
    }

    /**
     * Project final vertex onto preview line or find intersection with curved elements
     */
    protected projectFinalVertex(
        docCtrl: GeoDocCtrl,
        previewLine: RenderLine,
        finalVertex: RenderVertex,
        stroke: StrokeType
    ): RenderVertex {
        try {
            if (isElementLine(stroke)) return this.projectFinalVertexOnLine(finalVertex, stroke as RenderLine, docCtrl);

            if (isElementCurve(stroke))
                return this.projectFinalVertexOnCurvedElement(
                    previewLine,
                    finalVertex,
                    stroke as RenderCircle | RenderEllipse | RenderSector,
                    docCtrl
                );

            return finalVertex;
        } catch (error) {
            console.warn('Error projecting final vertex:', error);
            return finalVertex;
        }
    }

    /**
     * Project vertex onto preview line
     */
    protected projectVertexOntoPreviewLine(vertex: RenderVertex, docCtrl: GeoDocCtrl): RenderVertex {
        const projectedCoords = projectPointOntoLine(
            vertex.coords,
            this.throughPoint.coords,
            this.previewLine(docCtrl).orderedVector(docCtrl.rendererCtrl)
        );
        if (projectedCoords) {
            vertex.coords[0] = projectedCoords[0];
            vertex.coords[1] = projectedCoords[1];
            if (vertex.coords.length > 2) vertex.coords[2] = 0;
        }
        return vertex;
    }

    /**
     * Project final vertex on line stroke to intersection point
     */
    protected projectFinalVertexOnLine(vertex: RenderVertex, line: RenderLine, docCtrl: GeoDocCtrl): RenderVertex {
        try {
            const intersections = calculateLineLineIntersection(this.previewLine(docCtrl), line, docCtrl);
            if (intersections && intersections.length > 0) {
                // For line-line intersection, there's typically only one intersection
                // Use first intersection (no ordering needed for single point)
                const intersection = intersections[0];
                vertex.coords[0] = intersection.x;
                vertex.coords[1] = intersection.y;
                if (vertex.coords.length > 2) vertex.coords[2] = 0;
            }
            return vertex;
        } catch (error) {
            console.warn('Error projecting vertex on line:', error);
            return vertex;
        }
    }

    /**
     * Project final vertex on curved element to intersection point
     */
    protected projectFinalVertexOnCurvedElement(
        previewLine: RenderLine,
        vertex: RenderVertex,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        docCtrl: GeoDocCtrl
    ): RenderVertex {
        const intersections = filterVisibleIntersections(
            calculateIntersections(previewLine, curvedElement, docCtrl),
            previewLine,
            curvedElement,
            docCtrl
        );

        if (intersections.length > 0) {
            const selectedIntersectionIdx = this.findClosestIntersectionIdx(vertex, intersections);
            const selectedIntersection = intersections[selectedIntersectionIdx];

            vertex.coords[0] = selectedIntersection.x;
            vertex.coords[1] = selectedIntersection.y;

            if (vertex.coords.length > 2) vertex.coords[2] = 0;
        }

        return vertex;
    }

    /**
     * Find the closest intersection point to the given vertex
     * This is used for projection operations where we want the most natural/closest intersection
     */
    protected findClosestIntersectionIdx(vertex: RenderVertex, intersections: Point[]): number {
        if (intersections.length === 1) return 0;

        const pt = point(vertex.coords[0], vertex.coords[1]);
        let closestIntersectionIdx = 0;
        let minDistance = pt.distanceTo(intersections[0]);

        for (let i = 1; i < intersections.length; i++) {
            const distance = pt.distanceTo(intersections[i]);
            if (distance < minDistance) {
                minDistance = distance;
                closestIntersectionIdx = i;
            }
        }

        return closestIntersectionIdx;
    }

    /**
     * Calculate the nth intersection index based on the final vertex position
     * This determines which intersection the user actually selected through projection
     */
    protected calculateNthIntersectionFromVertex(
        previewLine: RenderLine,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        finalVertex: RenderVertex,
        docCtrl: GeoDocCtrl
    ): number {
        const intersections = filterVisibleIntersections(
            calculateIntersections(previewLine, curvedElement, docCtrl) || [],
            previewLine,
            curvedElement,
            docCtrl
        );

        if (intersections.length === 0) throw new Error('Không có giao điểm');
        if (intersections.length === 1) return 0;

        const orderedIntersections = getOrderedIntersections(previewLine, curvedElement, intersections, docCtrl);
        const closedPointIdx = this.findClosestIntersectionIdx(finalVertex, orderedIntersections);

        return closedPointIdx;
    }

    /**
     * Handle curved element construction with enhanced logic
     * Uses consistent intersection ordering and nth intersection selection
     */
    protected handleCurvedElementConstruction(
        line: RenderLine,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        finalVertex: RenderVertex,
        nthIntersection?: number
    ): any {
        // This method can be overridden by subclasses for specific curved element handling
        // Default implementation uses the abstract buildCurvedElementConstruction method
        const combinedName = `${throughPoint.name}${finalVertex.name}`;

        // For construction, use ordered intersection selection
        // If nth intersection is not specified, use first ordered intersection (not closest)
        const actualNthIntersection = nthIntersection !== undefined ? nthIntersection : 0;

        return this.buildCurvedElementConstruction(
            combinedName,
            line,
            curvedElement,
            throughPoint,
            actualNthIntersection
        );
    }

    /**
     * Handle simple line construction when final vertex is same as through point
     */
    protected async handleSimpleLineConstruction(
        docCtrl: GeoDocCtrl,
        baseLine: RenderLine,
        throughPoint: RenderVertex,
        previewLine: RenderLine
    ) {
        try {
            // Use assignNames with previewLine as target object
            const { pcs } = await assignNames(
                docCtrl,
                [throughPoint],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                '',
                this.getSimpleConstructionLabel(),
                previewLine
            );

            // Use build line construction
            const construction = this.buildSimpleLineConstruction(previewLine.name, baseLine, throughPoint);

            await remoteConstruct(
                docCtrl,
                construction,
                pcs,
                this.editor.geoGateway,
                this.getSimpleConstructionLabel()
            );
        } catch (error) {
            console.error('Error in simple line construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }

    /**
     * Handle complex line construction when final vertex is different from through point
     */
    protected async handleComplexLineConstruction(
        docCtrl: GeoDocCtrl,
        baseLine: RenderLine,
        throughPoint: RenderVertex,
        previewLine: RenderLine,
        finalVertex: RenderVertex,
        finalVertexStroke?: RenderLine | RenderCircle | RenderEllipse | RenderSector
    ) {
        try {
            // Project final vertex to appropriate position
            if (finalVertexStroke)
                finalVertex = this.projectFinalVertex(docCtrl, previewLine, finalVertex, finalVertexStroke);

            // Line segment to point - assign names for both line and endpoint
            const { pcs, points } = await assignNames(
                docCtrl,
                [throughPoint, finalVertex],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                'Tên điểm cuối',
                this.getComplexConstructionLabel()
            );

            if (!pcs || !points) {
                this.resetState();
                return;
            }

            throughPoint.name = points.find(p => p.relIndex === throughPoint.relIndex)?.name;
            finalVertex.name = points.find(p => p.relIndex === finalVertex.relIndex)?.name;

            // Create combined name for through point and end element
            const combinedName = `${throughPoint.name}${finalVertex.name}`;
            if (finalVertexStroke && isElementLine(finalVertexStroke)) {
                // Use intersection construction
                const construction = this.buildLineSegmentWithIntersectionConstruction(
                    combinedName,
                    baseLine,
                    finalVertexStroke as RenderLine,
                    throughPoint
                );
                await remoteConstruct(
                    docCtrl,
                    construction,
                    pcs.filter(pc => pc.name === throughPoint.name),
                    this.editor.geoGateway,
                    this.getComplexConstructionLabel()
                );
            } else if (finalVertexStroke && isElementCurve(finalVertexStroke)) {
                // Calculate which intersection the user actually selected based on finalVertex position
                const nthIntersection = this.calculateNthIntersectionFromVertex(
                    previewLine,
                    finalVertexStroke as RenderCircle | RenderEllipse | RenderSector,
                    finalVertex,
                    docCtrl
                );

                // Use curved element construction with the correct nth intersection
                const construction = this.handleCurvedElementConstruction(
                    baseLine,
                    finalVertexStroke as RenderCircle | RenderEllipse | RenderSector,
                    throughPoint,
                    finalVertex,
                    nthIntersection // Use the calculated intersection index
                );

                await remoteConstruct(
                    docCtrl,
                    construction,
                    pcs.filter(pc => pc.name === throughPoint.name),
                    this.editor.geoGateway,
                    this.getComplexConstructionLabel()
                );
            } else {
                // Check if finalVertex is a constructed point (exists in document)
                // If so, use segment between two specific points construction
                if (finalVertex.relIndex >= 0) {
                    const construction = this.buildSegmentBetweenPointsConstruction(
                        combinedName,
                        baseLine,
                        throughPoint,
                        finalVertex
                    );
                    await remoteConstruct(
                        docCtrl,
                        construction,
                        pcs.filter(pc => pc.name === throughPoint.name || pc.name === finalVertex.name),
                        this.editor.geoGateway,
                        this.getComplexConstructionLabel()
                    );
                } else {
                    // Use segment construction with scaling factor
                    const startPt = point(throughPoint.coords[0], throughPoint.coords[1]);
                    const endPt = point(finalVertex.coords[0], finalVertex.coords[1]);
                    const orderedVector = this.previewLine(docCtrl).orderedVector(docCtrl.rendererCtrl);
                    const orderedFVector = vector(orderedVector[0], orderedVector[1]);
                    const lineSegmentVector = vector(startPt, endPt);

                    // Calculate dot product to determine direction
                    const dotProduct = lineSegmentVector.dot(orderedFVector);

                    // Distance from through point to final vertex
                    const distance = lineSegmentVector.length;

                    // k is positive if same direction, negative if opposite direction
                    const k = dotProduct >= 0 ? distance : -distance;

                    const construction = this.buildLineSegmentConstruction(combinedName, baseLine, throughPoint, k);
                    await remoteConstruct(
                        docCtrl,
                        construction,
                        pcs.filter(pc => pc.name === throughPoint.name),
                        this.editor.geoGateway,
                        this.getComplexConstructionLabel()
                    );
                }
            }
        } catch (error) {
            console.error('Error in complex line construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') if (!this.shouldHandleClick(event)) return event;

        const docCtrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!docCtrl?.state) return event;

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, (event: any) =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, docCtrl))
            );
        else this.doTrySelection(event, docCtrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private createPreviewBaseLine(docCtrl: GeoDocCtrl) {
        const previewLine = pLine(
            docCtrl,
            this.previewBaseLineRelIdx,
            RenderLine,
            docCtrl.rendererCtrl.elementAt(this.baseLine.startPointIdx) as RenderVertex,
            undefined,
            this.baseLine.orderedVector(docCtrl.rendererCtrl)
        );

        // Set red color for highlighting
        previewLine.renderProp = buildPreviewLineRenderProp();
        previewLine.renderProp.lineColor = '#ff0000';
        previewLine.renderProp.lineWeight = 3;

        return previewLine;
    }

    /**
     * Implementation of abstract method to create highlighted preview for reference element
     */
    private createPreviewThroughPoint(): RenderVertex {
        const previewPoint = pVertex(this.previewThroughPointRelIdx, this.throughPoint.coords);
        previewPoint.renderProp = buildPreviewVertexRenderProp();
        previewPoint.renderProp.pointColor = '#ff0000';
        return previewPoint;
    }

    protected doTrySelection(event: GeoPointerEvent, docCtrl: GeoDocCtrl) {
        this.selLogic.trySelect(event, docCtrl);

        if (this.baseLine && !this.previewBaseLine(docCtrl)) this.pQ.add(this.createPreviewBaseLine(docCtrl));

        if (this.throughPoint && !this.previewThroughPoint(docCtrl)) this.pQ.add(this.createPreviewThroughPoint());

        if (this.baseLine && this.throughPoint) {
            if (!this.previewLine(docCtrl)) {
                const previewLine = pLine(
                    docCtrl,
                    this.previewLineRelIdx,
                    RenderLine,
                    this.throughPoint,
                    undefined,
                    this.calculateLinePreviewVector(docCtrl, this.baseLine)
                );
                this.pQ.add(previewLine);
            }
        } else if (this.previewLine(docCtrl)) syncRemovePreviewCmd(this.previewLine(docCtrl), docCtrl);

        this.pQ.flush(docCtrl);
    }
}
